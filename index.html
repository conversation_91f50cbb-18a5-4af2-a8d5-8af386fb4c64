<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>佛经宣传页 - 般若智慧</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="three-container"></div>
    
    <div class="content-overlay">
        <div class="image-section">
            <img src="images/2.png" alt="佛教宣传图" class="main-image">
        </div>

        <!-- 背景音乐 -->
        <audio id="background-music" autoplay loop preload="auto" muted style="display: none;">
            <source src="3333.mp3" type="audio/mpeg">
            您的浏览器不支持音频播放。
        </audio>

        <!-- 音乐提示 -->
        <div id="music-hint" style="position: fixed; top: 20px; right: 20px; z-index: 1000; background: rgba(255, 215, 0, 0.9); color: #000; padding: 10px 15px; border-radius: 25px; font-size: 14px; font-weight: bold; box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3); display: none; cursor: pointer; transition: all 0.3s ease;">
            🎵 点击开启背景音乐
        </div>
    </div>
    
    <div class="background-text" id="background-text"></div>
    
    <!-- 使用多个CDN源加载Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r154/three.min.js" onload="console.log('Three.js CDN 1 加载成功')" onerror="console.log('Three.js CDN 1 加载失败')"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/three.js/r154/three.min.js" onload="console.log('Three.js CDN 2 加载成功')" onerror="console.log('Three.js CDN 2 加载失败')"></script>
    <script src="https://unpkg.com/three@0.154.0/build/three.min.js" onload="console.log('Three.js CDN 3 加载成功')" onerror="console.log('Three.js CDN 3 加载失败')"></script>
    
    <script>
    // 冲屏发光金刚经动画特效
    const DIAMOND_SUTRA_TEXT = [
        '观自在菩萨', '行深般若波罗蜜多时', '照见五蕴皆空', '度一切苦厄',
        '舍利子', '色不异空', '空不异色', '色即是空', '空即是色',
        '受想行识', '亦复如是', '般若波罗蜜多心经', '如是我闻',
        '一时佛在', '祇树给孤独园', '与大比丘众', '千二百五十人俱',
        '尔时世尊', '食时著衣持钵', '入舍卫大城乞食', '于其城中',
        '次第乞已', '还至本处', '饭食讫', '收衣钵', '洗足已',
        '敷座而坐', '时长老须菩提', '在大众中', '即从座起',
        '偏袒右肩', '右膝著地', '合掌恭敬', '而白佛言',
        '希有世尊', '如来善护念诸菩萨', '善付嘱诸菩萨', '世尊',
        '善男子善女人', '发阿耨多罗三藐三菩提心', '应云何住',
        '云何降伏其心', '佛言', '善哉善哉', '须菩提',
        '如汝所说', '如来善护念诸菩萨', '善付嘱诸菩萨',
        '汝今谛听', '当为汝说', '应如是住', '如是降伏其心',
        '唯然世尊', '愿乐欲闻', '一切有为法', '如梦幻泡影',
        '如露亦如电', '应作如是观', '金刚般若波罗蜜经'
    ];

    class DiamondSutraEffect {
        constructor() {
            this.scene = null;
            this.camera = null;
            this.renderer = null;
            this.textObjects = [];
            this.particles = [];
            this.animationId = null;

            // 初始化背景音乐
            this.initBackgroundMusic();

            this.init();
        }

        initBackgroundMusic() {
            console.log('初始化背景音乐...');
            const audio = document.getElementById('background-music');
            const musicHint = document.getElementById('music-hint');

            if (audio) {
                // 先静音播放，然后取消静音（绕过自动播放限制）
                audio.muted = true;
                audio.volume = 0.3; // 30% 音量

                // 多种方式尝试自动播放
                const attemptAutoplay = () => {
                    // 方法1: 直接播放
                    const playPromise = audio.play();

                    if (playPromise !== undefined) {
                        playPromise.then(() => {
                            console.log('✅ 静音播放成功，准备取消静音...');

                            // 延迟取消静音
                            setTimeout(() => {
                                audio.muted = false;
                                console.log('✅ 背景音乐开始播放（已取消静音）');
                                if (musicHint) musicHint.style.display = 'none';
                            }, 500);

                        }).catch(error => {
                            console.log('⚠️ 静音播放也被阻止，尝试其他方法...');

                            // 方法2: 创建用户交互上下文
                            const forcePlay = () => {
                                audio.muted = false;
                                audio.play().then(() => {
                                    console.log('✅ 强制播放成功');
                                    if (musicHint) {
                                        musicHint.textContent = '🎵 音乐已开启';
                                        setTimeout(() => {
                                            musicHint.style.display = 'none';
                                        }, 2000);
                                    }
                                }).catch(e => {
                                    console.log('❌ 强制播放失败，显示用户提示');
                                    if (musicHint) {
                                        musicHint.style.display = 'block';
                                        musicHint.textContent = '🎵 点击开启背景音乐';
                                    }
                                });

                                // 移除监听器
                                document.removeEventListener('click', forcePlay);
                                document.removeEventListener('touchstart', forcePlay);
                                document.removeEventListener('keydown', forcePlay);
                                document.removeEventListener('mousemove', forcePlay);
                            };

                            // 监听各种用户交互
                            document.addEventListener('click', forcePlay);
                            document.addEventListener('touchstart', forcePlay);
                            document.addEventListener('keydown', forcePlay);
                            document.addEventListener('mousemove', forcePlay);

                            // 点击提示播放音乐
                            if (musicHint) {
                                musicHint.style.display = 'block';
                                musicHint.addEventListener('click', forcePlay);
                            }
                        });
                    }
                };

                // 立即尝试播放
                attemptAutoplay();

                // 备用方案：延迟再次尝试
                setTimeout(attemptAutoplay, 1000);
                setTimeout(attemptAutoplay, 3000);

            } else {
                console.error('❌ 找不到背景音乐元素');
            }
        }

        init() {
            console.log('DiamondSutraEffect 初始化开始...');
            setTimeout(() => {
                if (typeof THREE !== 'undefined') {
                    console.log('Three.js 加载成功，开始创建特效...');
                    this.setupThreeJS();
                    this.createTextEffect();
                    this.createParticleSystem();
                    this.animate();
                    this.handleResize();
                    console.log('特效创建完成！');
                } else {
                    console.error('Three.js还未加载完成，正在重试...');
                    // 延长等待时间并重试
                    setTimeout(() => this.init(), 500);

                    // 如果重试多次仍失败，使用CSS动画作为备用方案
                    if (!this.retryCount) this.retryCount = 0;
                    this.retryCount++;
                    if (this.retryCount > 5) {
                        console.log('启用CSS备用动画...');
                        this.createCSSFallback();
                    }
                }
            }, 100);
        }

        setupThreeJS() {
            console.log('设置 Three.js 场景...');
            this.scene = new THREE.Scene();
            this.scene.fog = new THREE.Fog(0x1a0f0a, 10, 500);

            this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            this.camera.position.z = 50;

            this.renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
            this.renderer.setSize(window.innerWidth, window.innerHeight);
            this.renderer.setClearColor(0x000000, 0); // 完全透明背景

            const container = document.getElementById('three-container');
            if (container) {
                container.appendChild(this.renderer.domElement);
                console.log('Three.js 渲染器已添加到容器');

                // 移除测试立方体
            } else {
                console.error('找不到 three-container 元素');
            }
        }

        createTextEffect() {
            console.log('创建文字特效，共', DIAMOND_SUTRA_TEXT.length, '个文字');
            DIAMOND_SUTRA_TEXT.forEach((text, index) => {
                this.createTextMesh(text, index);
            });
            console.log('文字特效创建完成，共创建了', this.textObjects.length, '个文字对象');
        }

        createTextMesh(text, index) {
            console.log('创建文字网格:', text);
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 768; // 增大画布宽度
            canvas.height = 192; // 增大画布高度

            // 清除画布
            context.clearRect(0, 0, canvas.width, canvas.height);
            context.fillStyle = 'rgba(0, 0, 0, 0)';
            context.fillRect(0, 0, canvas.width, canvas.height);

            // 设置复古字体，更大尺寸
            context.font = 'bold 96px "STKaiti", "KaiTi", "楷体", "FangSong", "仿宋", serif'; // 从64px增加到96px
            context.textAlign = 'center';
            context.textBaseline = 'middle';

            // 增强的发光效果
            context.shadowColor = '#FFD700';
            context.shadowBlur = 50; // 增强发光
            context.fillStyle = '#FFD700';
            context.fillText(text, canvas.width / 2, canvas.height / 2);

            // 第二层发光
            context.shadowBlur = 30;
            context.fillStyle = '#FFFF00';
            context.fillText(text, canvas.width / 2, canvas.height / 2);

            // 第三层发光
            context.shadowBlur = 15;
            context.fillStyle = '#FFFFFF';
            context.fillText(text, canvas.width / 2, canvas.height / 2);

            const texture = new THREE.CanvasTexture(canvas);
            const material = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                blending: THREE.AdditiveBlending,
                depthWrite: false,
                opacity: 1.0 // 确保完全不透明
            });

            const geometry = new THREE.PlaneGeometry(80, 20); // 进一步增大文字尺寸，从50x12增加到80x20
            const mesh = new THREE.Mesh(geometry, material);

            // 从屏幕边缘向中心冲击的初始位置
            const side = index % 4; // 0=左, 1=右, 2=上, 3=下
            let startX, startY, startZ;

            switch(side) {
                case 0: // 左边冲进来
                    startX = -80;
                    startY = (Math.random() - 0.5) * 60;
                    break;
                case 1: // 右边冲进来
                    startX = 80;
                    startY = (Math.random() - 0.5) * 60;
                    break;
                case 2: // 上边冲进来
                    startX = (Math.random() - 0.5) * 60;
                    startY = 60;
                    break;
                case 3: // 下边冲进来
                    startX = (Math.random() - 0.5) * 60;
                    startY = -60;
                    break;
            }

            startZ = -50 - Math.random() * 50;
            
            mesh.position.set(startX, startY, startZ);

            mesh.userData = {
                side: side,
                speed: 0.3 + Math.random() * 0.4, // 减慢速度
                rotationSpeed: (Math.random() - 0.5) * 0.05, // 减慢旋转
                phase: Math.random() * Math.PI * 2,
                amplitude: 8 + Math.random() * 15, // 增加振幅让文字更明显
                delay: index * 200 // 增加延迟间隔，让文字出现更有节奏
            };

            this.scene.add(mesh);
            this.textObjects.push(mesh);
            console.log('文字网格已添加到场景:', text, '位置:', mesh.position);
        }

        createParticleSystem() {
            // 创建卍字符粒子系统
            this.swastikaParticles = [];
            const particleCount = 100; // 减少数量以提高性能

            for (let i = 0; i < particleCount; i++) {
                // 创建卍字符纹理
                const canvas = document.createElement('canvas');
                canvas.width = 32;
                canvas.height = 32;
                const context = canvas.getContext('2d');

                // 绘制卍字符 - 调小字体
                context.fillStyle = '#FFD700';
                context.font = 'bold 16px serif'; // 从24px减小到16px
                context.textAlign = 'center';
                context.textBaseline = 'middle';
                context.fillText('卍', 16, 16);

                const texture = new THREE.CanvasTexture(canvas);

                // 创建精灵材质
                const material = new THREE.SpriteMaterial({
                    map: texture,
                    transparent: true,
                    opacity: 0.6 + Math.random() * 0.4,
                    blending: THREE.AdditiveBlending
                });

                const sprite = new THREE.Sprite(material);
                sprite.scale.set(4, 4, 1); // 从8减小到4，让卍字符更小

                // 随机位置
                sprite.position.set(
                    (Math.random() - 0.5) * 400,
                    (Math.random() - 0.5) * 400,
                    (Math.random() - 0.5) * 400
                );

                // 添加运动数据
                sprite.userData = {
                    velocity: new THREE.Vector3(
                        (Math.random() - 0.5) * 0.5,
                        (Math.random() - 0.5) * 0.5,
                        (Math.random() - 0.5) * 0.5
                    ),
                    rotationSpeed: (Math.random() - 0.5) * 0.02,
                    life: Math.random() * 300
                };

                this.scene.add(sprite);
                this.swastikaParticles.push(sprite);
            }
        }

        animate() {
            this.animationId = requestAnimationFrame(() => this.animate());

            const time = Date.now() * 0.001;

            // 文字冲屏动画
            this.textObjects.forEach((mesh, index) => {
                const userData = mesh.userData;
                
                // 延迟启动
                if (time * 1000 < userData.delay) return;
                
                // 向屏幕中心冲击
                const centerX = 0;
                const centerY = 0;
                const targetZ = 30;
                
                // 冲击运动 - 减慢速度
                mesh.position.x += (centerX - mesh.position.x) * 0.01; // 从0.02减慢到0.01
                mesh.position.y += (centerY - mesh.position.y) * 0.01;
                mesh.position.z += userData.speed;
                
                // 添加波浪扰动 - 减慢波动频率
                mesh.position.x += Math.sin(time * 1.5 + userData.phase) * userData.amplitude * 0.1; // 从3减慢到1.5
                mesh.position.y += Math.cos(time * 1.2 + userData.phase) * userData.amplitude * 0.1; // 从2.5减慢到1.2
                
                // 旋转
                mesh.rotation.z += userData.rotationSpeed;
                mesh.rotation.y += userData.rotationSpeed * 0.5;
                
                // 缩放脉冲 - 减慢脉冲频率
                const scale = 1 + Math.sin(time * 2 + index * 0.5) * 0.3; // 从4减慢到2
                mesh.scale.set(scale, scale, scale);
                
                // 重置位置（循环效果）
                if (mesh.position.z > 80) {
                    const side = userData.side;
                    switch(side) {
                        case 0:
                            mesh.position.x = -150;
                            mesh.position.y = (Math.random() - 0.5) * 100;
                            break;
                        case 1:
                            mesh.position.x = 150;
                            mesh.position.y = (Math.random() - 0.5) * 100;
                            break;
                        case 2:
                            mesh.position.x = (Math.random() - 0.5) * 100;
                            mesh.position.y = 100;
                            break;
                        case 3:
                            mesh.position.x = (Math.random() - 0.5) * 100;
                            mesh.position.y = -100;
                            break;
                    }
                    mesh.position.z = -100 - Math.random() * 50;
                }
            });

            // 卍字符粒子动画
            if (this.swastikaParticles) {
                this.swastikaParticles.forEach(particle => {
                    // 移动粒子
                    particle.position.add(particle.userData.velocity);

                    // 旋转粒子
                    particle.material.rotation += particle.userData.rotationSpeed;

                    // 添加飘动效果
                    particle.position.x += Math.sin(time * 0.5 + particle.position.y * 0.01) * 0.1;
                    particle.position.y += Math.cos(time * 0.3 + particle.position.x * 0.01) * 0.1;

                    // 边界检查和重置
                    if (particle.position.length() > 200) {
                        particle.position.set(
                            (Math.random() - 0.5) * 50,
                            (Math.random() - 0.5) * 50,
                            (Math.random() - 0.5) * 50
                        );
                    }

                    // 生命周期管理
                    particle.userData.life--;
                    if (particle.userData.life <= 0) {
                        particle.material.opacity = 0.6 + Math.random() * 0.4;
                        particle.userData.life = Math.random() * 300;
                    }
                });
            }

            // 相机震动效果 - 减弱震动
            this.camera.position.x = Math.sin(time * 0.2) * 0.5;
            this.camera.position.y = Math.cos(time * 0.15) * 0.3;
            this.camera.lookAt(0, 0, 0);

            this.renderer.render(this.scene, this.camera);
        }

        handleResize() {
            window.addEventListener('resize', () => {
                if (this.camera && this.renderer) {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                }
            });
        }

        createCSSFallback() {
            console.log('创建CSS备用动画...');
            const container = document.getElementById('three-container');
            if (!container) return;

            // 创建CSS动画文字
            DIAMOND_SUTRA_TEXT.forEach((text, index) => {
                const textElement = document.createElement('div');
                textElement.textContent = text;
                textElement.style.cssText = `
                    position: absolute;
                    font-size: 6rem; /* 从4rem增加到6rem，让经文更大 */
                    font-weight: bold;
                    font-family: "STKaiti", "KaiTi", "楷体", "FangSong", "仿宋", serif;
                    color: #FFD700;
                    text-shadow:
                        0 0 30px #FFD700,
                        0 0 60px #FFA500,
                        0 0 90px #FFD700,
                        0 0 120px #FFFF00; /* 增强发光效果 */
                    animation: cssTextFly ${10 + Math.random() * 6}s linear infinite; /* 从6-10秒改为10-16秒 */
                    animation-delay: ${index * 0.8}s; /* 从0.5秒增加到0.8秒延迟 */
                    white-space: nowrap;
                    pointer-events: none;
                    z-index: 20;
                `;

                // 随机起始位置
                const side = index % 4;
                switch(side) {
                    case 0: // 左
                        textElement.style.left = '-200px';
                        textElement.style.top = Math.random() * window.innerHeight + 'px';
                        break;
                    case 1: // 右
                        textElement.style.right = '-200px';
                        textElement.style.top = Math.random() * window.innerHeight + 'px';
                        break;
                    case 2: // 上
                        textElement.style.top = '-100px';
                        textElement.style.left = Math.random() * window.innerWidth + 'px';
                        break;
                    case 3: // 下
                        textElement.style.bottom = '-100px';
                        textElement.style.left = Math.random() * window.innerWidth + 'px';
                        break;
                }

                container.appendChild(textElement);
            });

            // 添加CSS动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes cssTextFly {
                    0% { transform: scale(0.5) rotate(0deg); opacity: 0; }
                    20% { opacity: 1; }
                    50% { transform: scale(1.2) rotate(180deg); }
                    80% { opacity: 1; }
                    100% { transform: scale(0.5) rotate(360deg); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // 启动特效
    console.log('页面脚本开始执行...');

    function startEffect() {
        console.log('启动金刚经特效...');
        new DiamondSutraEffect();

        // 额外的音乐自动播放尝试
        setTimeout(() => {
            const audio = document.getElementById('background-music');
            if (audio && audio.paused) {
                console.log('尝试额外的音乐自动播放...');
                audio.muted = false;
                audio.play().catch(() => {
                    console.log('额外尝试也失败，等待用户交互');
                });
            }
        }, 2000);
    }

    // 多种方式确保特效启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', startEffect);
    } else {
        // DOM 已经加载完成
        startEffect();
    }

    // 备用启动方式
    window.addEventListener('load', () => {
        if (!document.querySelector('#three-container canvas')) {
            console.log('备用启动方式激活...');
            setTimeout(startEffect, 1000);
        }
    });

    // 手动启动函数
    window.forceStartEffect = function() {
        console.log('手动启动特效...');
        startEffect();
    };
    </script>
</body>
</html> 