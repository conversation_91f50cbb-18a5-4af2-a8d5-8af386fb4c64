<!DOCTYPE html>
<html>
<head>
    <title>Three.js Test</title>
    <style>
        body { margin: 0; background: black; }
        #container { width: 100%; height: 100vh; }
    </style>
</head>
<body>
    <div id="container"></div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r154/three.min.js"></script>
    <script>
        console.log('开始测试 Three.js...');
        console.log('THREE 对象:', typeof THREE);
        
        if (typeof THREE !== 'undefined') {
            console.log('Three.js 加载成功！');
            
            // 创建基本场景
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            const renderer = new THREE.WebGLRenderer();
            
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById('container').appendChild(renderer.domElement);
            
            // 创建一个简单的立方体
            const geometry = new THREE.BoxGeometry();
            const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
            const cube = new THREE.Mesh(geometry, material);
            scene.add(cube);
            
            camera.position.z = 5;
            
            // 动画循环
            function animate() {
                requestAnimationFrame(animate);
                cube.rotation.x += 0.01;
                cube.rotation.y += 0.01;
                renderer.render(scene, camera);
            }
            
            animate();
            console.log('Three.js 测试场景创建成功！');
        } else {
            console.error('Three.js 未加载！');
        }
    </script>
</body>
</html>
