/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'SimSun', serif;
    overflow: hidden;
    background: radial-gradient(ellipse at center, #1a0f0a, #2d1810, #0f0a08);
    height: 100vh;
    position: relative;
}

/* Three.js 容器 */
#three-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 15; /* 提高z-index，让文字特效显示在最上层 */
    pointer-events: none; /* 允许点击穿透到下层元素 */
}

/* 内容覆盖层 */
.content-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 2rem;
}

/* 图片区域 */
.image-section {
    flex: 0 0 auto;
    max-width: 55%;
    position: relative;
    background: transparent; /* 确保背景透明 */
}

.main-image {
    width: 100%;
    max-width: 650px;
    height: auto;
    /* 去除圆角以避免边框效果 */
    /* 完全去除边框和阴影效果 */
    border: none;
    box-shadow: none;
    filter: none;
    transition: all 0.3s ease;
    /* 移除任何可能的背景 */
    background: transparent;
    /* 添加淡淡的金光闪烁动画 */
    animation: buddhaGlow 4s ease-in-out infinite;
}

.main-image:hover {
    transform: scale(1.02);
    /* 去除hover时的阴影效果 */
}

/* 文字覆盖层 */
.text-overlay {
    flex: 1;
    padding-left: 3rem;
    text-align: left;
}

/* 金色标题效果 */
.golden-title {
    font-size: 4.5rem;
    background: linear-gradient(45deg, 
        #FFD700, 
        #FFA500, 
        #FFFF00, 
        #FFD700,
        #FFA500,
        #FFEC8C
    );
    background-size: 600% 600%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 
        0 0 20px rgba(255, 215, 0, 1),
        0 0 40px rgba(255, 165, 0, 0.8),
        0 0 60px rgba(255, 215, 0, 0.6),
        0 0 80px rgba(255, 215, 0, 0.4);
    animation: goldShimmer 2s ease-in-out infinite, textPulse 3s ease-in-out infinite;
    margin-bottom: 1rem;
    font-weight: 900;
    letter-spacing: 0.15em;
    transform-origin: center;
}

.golden-subtitle {
    font-size: 1.8rem;
    color: #FFD700;
    text-shadow: 
        0 0 5px rgba(255, 215, 0, 0.6),
        0 0 10px rgba(255, 215, 0, 0.4),
        0 0 15px rgba(255, 215, 0, 0.2);
    opacity: 0.9;
    font-weight: 300;
    letter-spacing: 0.05em;
    line-height: 1.6;
}

/* 背景文字层 */
.background-text {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5; /* 在图片上方，但在Three.js特效下方 */
    pointer-events: none;
    overflow: hidden;
    font-size: 14px;
    color: rgba(255, 215, 0, 0.1);
    line-height: 1.8;
    white-space: pre-wrap;
    animation: textFlow 35s linear infinite; /* 从20秒增加到35秒，让背景文字流动更慢 */
}

/* 金色闪烁动画 */
@keyframes goldShimmer {
    0%, 100% {
        background-position: 0% 50%;
        filter: brightness(1) contrast(1);
    }
    25% {
        background-position: 50% 100%;
        filter: brightness(1.3) contrast(1.2);
    }
    50% {
        background-position: 100% 50%;
        filter: brightness(1.5) contrast(1.4);
    }
    75% {
        background-position: 50% 0%;
        filter: brightness(1.3) contrast(1.2);
    }
}

/* 文字脉冲动画 */
@keyframes textPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* 佛像光辉动画 */
@keyframes buddhaGlow {
    0%, 100% {
        filter: drop-shadow(0 0 30px rgba(255, 215, 0, 1)) brightness(1);
    }
    50% {
        filter: drop-shadow(0 0 50px rgba(255, 215, 0, 1.2)) brightness(1.1);
    }
}

/* 佛像金光闪烁动画 */
@keyframes buddhaGlow {
    0%, 100% {
        filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.3));
    }
    50% {
        filter: drop-shadow(0 0 40px rgba(255, 215, 0, 0.6));
    }
}

/* 文字流动动画 */
@keyframes textFlow {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0.05;
    }
    15% {
        opacity: 0.1;
    }
    85% {
        opacity: 0.1;
    }
    100% {
        transform: translateY(-100vh) rotate(90deg); /* 添加缓慢旋转 */
        opacity: 0.05;
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .content-overlay {
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 1rem;
    }
    
    .image-section {
        max-width: 70%;
        margin-bottom: 2rem;
    }
    
    .text-overlay {
        padding-left: 0;
        text-align: center;
    }
    
    .golden-title {
        font-size: 2.5rem;
    }
    
    .golden-subtitle {
        font-size: 1.4rem;
    }
}

@media (max-width: 768px) {
    .golden-title {
        font-size: 2rem;
    }
    
    .golden-subtitle {
        font-size: 1.2rem;
    }
    
    .image-section {
        max-width: 90%;
    }
    
    .content-overlay {
        padding: 0.5rem;
    }
}

/* 自定义滚动条（如果需要） */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #FFD700, #FFA500);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #FFFF00, #FFD700);
} 