<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UNO游戏玩法介绍</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
            color: #333;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: bounceIn 1s ease-out;
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        .title {
            font-size: 4rem;
            color: #fff;
            text-shadow: 3px 3px 0px #ff4757, 6px 6px 10px rgba(0,0,0,0.3);
            margin-bottom: 10px;
            transform: rotate(-2deg);
        }

        .subtitle {
            font-size: 1.5rem;
            color: #fff;
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            backdrop-filter: blur(10px);
        }

        .card-section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            animation: slideInUp 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }

        .card-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @keyframes slideInUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .section-title {
            font-size: 2.5rem;
            color: #ff4757;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '🎮';
            position: absolute;
            right: -50px;
            top: 0;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            color: white;
            transform: perspective(1000px) rotateY(0deg);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .card:hover {
            transform: perspective(1000px) rotateY(10deg) scale(1.05);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .rules-list {
            list-style: none;
            padding: 0;
        }

        .rules-list li {
            background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%);
            margin: 15px 0;
            padding: 15px 20px;
            border-radius: 25px;
            position: relative;
            animation: slideInLeft 0.6s ease-out;
            animation-fill-mode: both;
        }

        .rules-list li:nth-child(1) { animation-delay: 0.1s; }
        .rules-list li:nth-child(2) { animation-delay: 0.2s; }
        .rules-list li:nth-child(3) { animation-delay: 0.3s; }
        .rules-list li:nth-child(4) { animation-delay: 0.4s; }
        .rules-list li:nth-child(5) { animation-delay: 0.5s; }

        @keyframes slideInLeft {
            from { transform: translateX(-100px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .rules-list li::before {
            content: '🎯';
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .fun-fact {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
            position: relative;
            animation: wiggle 3s ease-in-out infinite;
        }

        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(1deg); }
            75% { transform: rotate(-1deg); }
        }

        .fun-fact::before {
            content: '💡';
            font-size: 2rem;
            position: absolute;
            top: -10px;
            left: 20px;
            animation: spin 4s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 30px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .footer h3 {
            color: #fff;
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .footer p {
            color: #fff;
            font-size: 1.2rem;
        }

        .floating-cards {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-card {
            position: absolute;
            font-size: 2rem;
            animation: float 6s ease-in-out infinite;
            opacity: 0.3;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .floating-card:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-card:nth-child(2) { top: 20%; right: 10%; animation-delay: 1s; }
        .floating-card:nth-child(3) { bottom: 30%; left: 15%; animation-delay: 2s; }
        .floating-card:nth-child(4) { bottom: 10%; right: 20%; animation-delay: 3s; }
    </style>
</head>
<body>
    <div class="floating-cards">
        <div class="floating-card">🃏</div>
        <div class="floating-card">🎴</div>
        <div class="floating-card">🃏</div>
        <div class="floating-card">🎴</div>
    </div>

    <div class="container">
        <header class="header">
            <h1 class="title">UNO</h1>
            <p class="subtitle">🎉 最受欢迎的卡牌游戏 🎉</p>
        </header>

        <section class="card-section">
            <h2 class="section-title">游戏目标</h2>
            <div class="fun-fact">
                <h3>🏆 成为第一个出完所有手牌的玩家！</h3>
                <p>当你只剩一张牌时，记得大喊"UNO"！</p>
            </div>
        </section>

        <section class="card-section">
            <h2 class="section-title">卡牌类型</h2>
            <div class="cards-grid">
                <div class="card">
                    <div class="card-icon">🔢</div>
                    <h3>数字牌</h3>
                    <p>0-9，四种颜色<br>红、黄、绿、蓝</p>
                </div>
                <div class="card">
                    <div class="card-icon">🚫</div>
                    <h3>跳过牌</h3>
                    <p>跳过下一位玩家的回合</p>
                </div>
                <div class="card">
                    <div class="card-icon">🔄</div>
                    <h3>反转牌</h3>
                    <p>改变游戏进行方向</p>
                </div>
                <div class="card">
                    <div class="card-icon">➕</div>
                    <h3>+2牌</h3>
                    <p>下一位玩家摸2张牌并跳过回合</p>
                </div>
                <div class="card">
                    <div class="card-icon">🌈</div>
                    <h3>变色牌</h3>
                    <p>可以改变当前颜色</p>
                </div>
                <div class="card">
                    <div class="card-icon">💥</div>
                    <h3>+4变色牌</h3>
                    <p>下一位玩家摸4张牌，跳过回合，并改变颜色</p>
                </div>
            </div>
        </section>

        <section class="card-section">
            <h2 class="section-title">游戏规则</h2>
            <ul class="rules-list">
                <li>每位玩家开始时拿7张牌</li>
                <li>按颜色或数字匹配出牌</li>
                <li>无法出牌时必须从牌堆摸一张</li>
                <li>只剩一张牌时必须喊"UNO"</li>
                <li>忘记喊"UNO"被发现要罚摸2张牌</li>
            </ul>
        </section>

        <footer class="footer">
            <h3>🎊 准备好挑战了吗？</h3>
            <p>召集朋友们，开始这场精彩的UNO对决吧！</p>
        </footer>
    </div>
</body>
</html>
