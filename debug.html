<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <style>
        body { 
            margin: 0; 
            background: black; 
            color: white; 
            font-family: Arial, sans-serif;
        }
        #debug { 
            position: fixed; 
            top: 10px; 
            left: 10px; 
            z-index: 1000; 
            background: rgba(0,0,0,0.8); 
            padding: 10px; 
            border-radius: 5px;
            max-width: 300px;
        }
        #container { 
            width: 100%; 
            height: 100vh; 
            position: relative;
        }
        .test-text {
            position: absolute;
            font-size: 2rem;
            color: #FFD700;
            text-shadow: 0 0 20px #FFD700;
            animation: fly 3s linear infinite;
        }
        @keyframes fly {
            0% { 
                transform: translateX(-100px) scale(0.5); 
                opacity: 0; 
            }
            50% { 
                transform: translateX(50vw) scale(1.2); 
                opacity: 1; 
            }
            100% { 
                transform: translateX(100vw) scale(0.5); 
                opacity: 0; 
            }
        }
    </style>
</head>
<body>
    <div id="debug">
        <h3>调试信息:</h3>
        <div id="log"></div>
    </div>
    
    <div id="container"></div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r154/three.min.js"></script>
    <script>
        const log = document.getElementById('log');
        
        function addLog(message) {
            console.log(message);
            log.innerHTML += '<div>' + message + '</div>';
        }
        
        addLog('页面开始加载...');
        addLog('Three.js 状态: ' + (typeof THREE !== 'undefined' ? '已加载' : '未加载'));
        
        // 测试CSS动画
        function createTestText() {
            addLog('创建测试文字...');
            const texts = ['观自在菩萨', '行深般若波罗蜜多时', '照见五蕴皆空'];
            
            texts.forEach((text, index) => {
                const element = document.createElement('div');
                element.textContent = text;
                element.className = 'test-text';
                element.style.top = (100 + index * 100) + 'px';
                element.style.animationDelay = (index * 0.5) + 's';
                document.getElementById('container').appendChild(element);
            });
            
            addLog('CSS动画文字已创建');
        }
        
        // 测试Three.js
        function testThreeJS() {
            if (typeof THREE !== 'undefined') {
                addLog('开始测试 Three.js...');
                
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                const renderer = new THREE.WebGLRenderer({ alpha: true });
                
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setClearColor(0x000000, 0.1);
                document.getElementById('container').appendChild(renderer.domElement);
                
                // 创建测试立方体
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                const cube = new THREE.Mesh(geometry, material);
                scene.add(cube);
                
                camera.position.z = 5;
                
                function animate() {
                    requestAnimationFrame(animate);
                    cube.rotation.x += 0.01;
                    cube.rotation.y += 0.01;
                    renderer.render(scene, camera);
                }
                
                animate();
                addLog('Three.js 测试成功！');
            } else {
                addLog('Three.js 未加载，使用CSS动画');
                createTestText();
            }
        }
        
        // 启动测试
        setTimeout(() => {
            testThreeJS();
        }, 1000);
        
        // 无论如何都创建CSS动画
        setTimeout(() => {
            createTestText();
        }, 2000);
    </script>
</body>
</html>
