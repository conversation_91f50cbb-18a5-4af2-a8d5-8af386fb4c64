#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
佛教金刚经特效页面服务器启动脚本
启动HTTP服务器来托管index.html页面，支持外网映射
"""

import http.server
import socketserver
import os
import sys
import webbrowser
import socket
import threading
import time
from datetime import datetime

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def end_headers(self):
        # 添加CORS头，允许跨域访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {format % args}")

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 连接到一个远程地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=8080):
    """查找可用端口"""
    port = start_port
    while port < start_port + 100:
        if check_port_available(port):
            return port
        port += 1
    return None

def open_browser_delayed(url, delay=2):
    """延迟打开浏览器"""
    def delayed_open():
        time.sleep(delay)
        try:
            webbrowser.open(url)
            print(f"✅ 浏览器已自动打开: {url}")
        except Exception as e:
            print(f"❌ 无法自动打开浏览器: {e}")
    
    thread = threading.Thread(target=delayed_open)
    thread.daemon = True
    thread.start()

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🕉️  佛教金刚经特效服务器  🕉️                    ║
║                                                              ║
║                        南无阿弥陀佛                            ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def print_access_info(port, local_ip):
    """打印访问信息"""
    print("\n" + "="*60)
    print("🌐 服务器启动成功！")
    print("="*60)
    print(f"📍 本地访问地址:")
    print(f"   • http://localhost:{port}/welcome.html (欢迎页)")
    print(f"   • http://localhost:{port}/index.html (直接进入)")
    print(f"   • http://127.0.0.1:{port}/welcome.html")
    print(f"\n🌍 局域网访问地址:")
    print(f"   • http://{local_ip}:{port}/welcome.html")
    print(f"\n📱 外网映射建议:")
    print(f"   • 使用 ngrok: ngrok http {port}")
    print(f"   • 使用 frp 映射端口 {port}")
    print(f"   • 路由器端口转发: {port} -> {local_ip}:{port}")
    print("\n" + "="*60)
    print("💡 提示:")
    print("   • 按 Ctrl+C 停止服务器")
    print("   • 服务器支持跨域访问")
    print("   • 页面包含佛教金刚经3D特效")
    print("="*60)

def main():
    """主函数"""
    print_banner()
    
    # 检查index.html是否存在
    if not os.path.exists('index.html'):
        print("❌ 错误: 找不到 index.html 文件")
        print("请确保脚本与 index.html 在同一目录下")
        sys.exit(1)
    
    # 查找可用端口
    port = find_available_port(8080)
    if port is None:
        print("❌ 错误: 无法找到可用端口 (8080-8179)")
        sys.exit(1)
    
    # 获取本机IP
    local_ip = get_local_ip()
    
    try:
        # 创建服务器
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print_access_info(port, local_ip)
            
            # 延迟打开浏览器（打开欢迎页）
            open_browser_delayed(f"http://localhost:{port}/welcome.html")
            
            print(f"\n🚀 服务器正在运行... (端口: {port})")
            print("🙏 愿此功德回向法界众生，南无阿弥陀佛")
            print("\n按 Ctrl+C 停止服务器\n")
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        print("🙏 南无阿弥陀佛，功德圆满")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
