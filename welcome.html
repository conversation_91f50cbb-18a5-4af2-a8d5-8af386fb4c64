<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🕉️ 金刚经特效 - 欢迎页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "STKaiti", "KaiTi", "楷体", "FangSong", "仿宋", serif;
            background: linear-gradient(135deg, #1a0f0a 0%, #2d1810 50%, #1a0f0a 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #FFD700;
            overflow: hidden;
            position: relative;
        }

        /* 背景粒子效果 */
        .background-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            color: #FFD700;
            opacity: 0.3;
            font-size: 20px;
            animation: float 8s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.3;
            }
            90% {
                opacity: 0.3;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .welcome-container {
            text-align: center;
            z-index: 10;
            max-width: 600px;
            padding: 2rem;
        }

        .main-title {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 
                0 0 20px #FFD700,
                0 0 40px #FFA500,
                0 0 60px #FFD700;
            animation: titleGlow 3s ease-in-out infinite;
        }

        @keyframes titleGlow {
            0%, 100% {
                text-shadow: 
                    0 0 20px #FFD700,
                    0 0 40px #FFA500,
                    0 0 60px #FFD700;
            }
            50% {
                text-shadow: 
                    0 0 30px #FFD700,
                    0 0 60px #FFA500,
                    0 0 90px #FFD700,
                    0 0 120px #FFFF00;
            }
        }

        .subtitle {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .enter-button {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            border: none;
            padding: 20px 50px;
            font-size: 1.8rem;
            font-weight: bold;
            color: #000;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 
                0 8px 25px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            font-family: inherit;
            animation: buttonPulse 2s ease-in-out infinite;
        }

        @keyframes buttonPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 
                    0 8px 25px rgba(255, 215, 0, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 
                    0 12px 35px rgba(255, 215, 0, 0.5),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
        }

        .enter-button:hover {
            transform: scale(1.1);
            box-shadow: 
                0 15px 40px rgba(255, 215, 0, 0.6),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .enter-button:active {
            transform: scale(0.98);
        }

        .blessing {
            margin-top: 2rem;
            font-size: 1.2rem;
            opacity: 0.7;
            animation: fadeInOut 4s ease-in-out infinite;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        /* 移除音乐图标样式 */
    </style>
</head>
<body>
    <!-- 背景粒子 -->
    <div class="background-particles" id="particles"></div>

    <!-- 主要内容 -->
    <div class="welcome-container">
        <h1 class="main-title">🕉️ 金刚经特效</h1>
        <p class="subtitle">
            体验神圣的佛教经文3D特效<br>
            伴随《大威天龙》背景音乐<br>
            感受佛法的庄严与慈悲
        </p>
        
        <button class="enter-button" onclick="enterExperience()">
            🙏 进入体验
        </button>
        
        <p class="blessing">南无阿弥陀佛 🙏 功德无量</p>
    </div>

    <script>
        // 创建背景粒子
        function createParticles() {
            const container = document.getElementById('particles');
            const symbols = ['卍', '🕉️', '☸️', '🙏'];
            
            setInterval(() => {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.textContent = symbols[Math.floor(Math.random() * symbols.length)];
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDuration = (5 + Math.random() * 5) + 's';
                particle.style.fontSize = (15 + Math.random() * 10) + 'px';
                
                container.appendChild(particle);
                
                // 8秒后移除粒子
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 8000);
            }, 800);
        }

        // 进入体验
        function enterExperience() {
            // 添加点击效果
            const button = document.querySelector('.enter-button');
            button.style.transform = 'scale(0.95)';
            button.textContent = '正在启动...';
            
            setTimeout(() => {
                // 跳转到主页面
                window.location.href = 'index.html';
            }, 1000);
        }

        // 启动粒子效果
        createParticles();

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                enterExperience();
            }
        });
    </script>
</body>
</html>
